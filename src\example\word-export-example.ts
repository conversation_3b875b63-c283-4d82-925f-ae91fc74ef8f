import { WordExportService } from '../service/word-export.service';
import { FileManagerService } from '../service/file-manager.service';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Word导出示例
 * 展示如何在项目中使用WordExportService
 */
async function main() {
  try {
    console.log('开始执行Word导出示例...');

    // 创建服务实例并手动注入依赖
    const fileManagerService = new FileManagerService();
    const wordExportService = new WordExportService();
    wordExportService.fileManagerService = fileManagerService;

    // 读取HTML文件内容
    const htmlFilePath = path.join(
      __dirname,
      '../../testFiles/input-html-sample.html'
    );
    const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');

    console.log(`已读取HTML文件: ${htmlFilePath}`);

    // 设置导出选项
    const exportOptions = {
      title: '试题导出示例',
      author: '系统导出',
      fileName: '试题导出.docx',
      orientation: 'portrait' as 'portrait' | 'landscape',
    };

    // 设置输出路径
    const outputPath = path.join(
      __dirname,
      '../../testFiles/output-word-result.docx'
    );

    // 将HTML内容转换为Word文档并保存
    console.log('开始转换HTML到Word...');
    const savedPath = await wordExportService.exportHtmlToWordFile(
      htmlContent,
      outputPath,
      exportOptions
    );

    console.log(`Word文档保存成功: ${savedPath}`);

    // 输出图片处理信息
    console.log('\n=== 图片处理调试信息 ===');
    console.log('HTML中的图片数量:', (htmlContent.match(/<img/g) || []).length);
    console.log('Base64图片数量:', (htmlContent.match(/data:image\//g) || []).length);

    // 检查HTML中的图片信息
    const imgMatches = htmlContent.match(/<img[^>]*>/g);
    if (imgMatches) {
      imgMatches.forEach((img, index) => {
        console.log(`图片 ${index + 1}:`, img.substring(0, 100) + '...');
        const srcMatch = img.match(/src="([^"]*)/);
        if (srcMatch) {
          const src = srcMatch[1];
          if (src.startsWith('data:image/')) {
            console.log(`  - Base64图片，类型: ${src.split(';')[0].split(':')[1]}`);
            console.log(`  - 数据长度: ${src.length} 字符`);
          } else {
            console.log(`  - 外部图片: ${src}`);
          }
        }
      });
    }

    // 也可以获取Word文档的Buffer，用于其他用途
    // const docxBuffer = await wordExportService.exportHtmlToWord(htmlContent, exportOptions);
    // console.log(`生成的Word文档大小: ${docxBuffer.length} 字节`);
  } catch (error) {
    console.error('Word导出示例执行失败:', error);
  }
}

// 仅在直接运行此文件时执行示例
if (require.main === module) {
  main().catch(console.error);
}
